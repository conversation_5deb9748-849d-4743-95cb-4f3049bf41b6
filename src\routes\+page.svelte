<script lang="ts">
	import { fly } from 'svelte/transition';
    import Header from './Header.svelte';

    let formState = $state({
        answers: {
            question: '', type: '', id: ''
        }: {
            question: string,
            type: String,
            id: String,
        },
        step: 0,
        error: ""
    })

    // watches a specific value. re-runs whenever the state updates
    // useful for debugging
    $inspect(formState.step)

    const QUESTIONS = [
        {
            question: 'What is your name?',
            id: 'name',
            type: 'text'
        },
        {
            question: 'What is birthday?',
            id: 'birthday',
            type: 'date'
        },
        {
            question: 'What is your favorite color?',
            id: 'color',
            type: 'color'
        }
    ];

    function nextStep(id: string) {
        if (formState.answers[id]) {
            formState.step += 1;
            formState.error = '';
        } else {
            formState.error = 'Please fill out the form input';
        }
    }

    // will run onMount
    /* $effect(() => {
        console.log('onMount');
        return () => {
            // will run when unmounted or destroyed
            // will also run before the effect re-runs
            console.log('onUnmount')
        }
    }) */

    /* $effect(() => {
        // this will re-run, when formState.step has changed
        // basically, the effect re-runs whenever a state has changed
        console.log('formState', formState.step);

        // DON'T create state based off other state, in effect
        // Use $derived() instead
        return () => {
            // will run before the EFFECT re-runs (do not confuse with before the STATE CHANGES)
            console.log('before formState reruns', formState.step)
        }
    }) */
</script>

<!-- <Header name = {formState.name}>
    <p>Hello</p>
    {#snippet secondChild(name)}
        <p>Second Child {name}</p>
    {/snippet}
</Header> -->
<Header name = {formState.answers.name}/>
<main>
    {#if formState.step >= QUESTIONS.length}
        <p>Thank you!</p>
    {:else}
        <p>Step: {formState.step + 1}</p>
    {/if}
    <!-- Default way -->
    <!-- {#each QUESTIONS as question (question.id)}
        {@render formStep({ question: "What's your name", id: "name", type: "text"})}
        {/each} 
     -->
    <!-- Destructured way -->
    <!-- {#each QUESTIONS as {id, question, type} (id)}
        {@render formStep({ question, id, type })}
    {/each} -->
    <!-- If the properties are 1:1 -->
    {#each QUESTIONS as question, index (question.id)}
        {#if formState.step === index}
            <div
                in:fly={{ x: 200, duration: 200, opacity: 0, delay: 200 }}
                out:fly={{ x: -200, duration: 200, opacity: 0 }}
            >
                {@render formStep(question)}
            </div>
        {/if}
    {/each}

    {#if formState.error !== ''}
        <p class="error">Error: {formState.error}</p>
    {/if}
</main>

<!-- Show the state -->
<!-- {JSON.stringify(formState)} -->

{#snippet formStep({ question, id, type }: {
    type: string;
    id: string;
    question: string
})}
    <article>
        <div>
            <label for={id}>{question}</label>
            <input {type} {id} bind:value={formState.answers[id]}/>
        </div>
        <button onclick={() => nextStep(id)}>Next</button>
    </article>
{/snippet}


<!-- CSS in svelte component is tightly scoped -->
<!-- It is recommended that the styles be put in a global css file -->
<!-- 
    to add global css, use:
    :global(selector) {

    }
-->
<style>
    .error {
        color: red;
    }
</style>