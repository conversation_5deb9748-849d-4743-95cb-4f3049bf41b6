// Classic Way
export function createState () {
    let value = $state(0);

    function up () {
        value += 1;
    }

    return {
        // this returns the most up to date value of the state
        // value in the function name is a reserved keyword
        get value () {
            return value;
        },
        set value (newValue) {
            value = newValue;
        },
        up
    };
}

// Better way
export class CustomState {
    value = $state(0);

    up () {
        this.value += 1;
    }
}
