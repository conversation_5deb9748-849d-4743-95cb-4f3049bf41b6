import prettier from 'eslint-config-prettier';
import { includeIgnoreFile } from '@eslint/compat';
import js from '@eslint/js';
import svelte from 'eslint-plugin-svelte';
import globals from 'globals';
import { fileURLToPath } from 'node:url';
import ts from 'typescript-eslint';
import svelteConfig from './svelte.config.js';

const gitignorePath = fileURLToPath(new URL('./.gitignore', import.meta.url));

export default ts.config(
	includeIgnoreFile(gitignorePath),
	js.configs.recommended,
	...ts.configs.recommended,
	...svelte.configs.recommended,
	prettier,
	...svelte.configs.prettier,
	{
		languageOptions: {
			globals: { ...globals.browser, ...globals.node }
		},
		rules: {
			// typescript-eslint strongly recommend that you do not use the no-undef lint rule on TypeScript projects.
			// see: https://typescript-eslint.io/troubleshooting/faqs/eslint/#i-get-errors-from-the-no-undef-rule-about-global-variables-not-being-defined-even-though-there-are-no-typescript-errors
			'no-undef': 'off',
			"comma-dangle": ["error", "never"],
			"brace-style": "off",
			"space-before-function-paren": ["error", {
				"anonymous": "always",
				"named": "always",
				"asyncArrow": "always"
			}],
			"no-tabs": "off",
			"indent": "off",
			"no-eval": "off",
			"no-extra-bind": "off",
			"no-useless-return": "off",
			"camelcase": "off",
			"no-redeclare": "off",
			"semi": ["warn", "always"], // Note: 1 is equivalent to "warn"
			"object-curly-spacing": ["error", "always"],
			"key-spacing": ["error", { "beforeColon": false, "afterColon": true }],
			"comma-spacing": ["error", { "before": false, "after": true }],
			"space-infix-ops": "error",
			"keyword-spacing": ["error", { "before": true, "after": true }],
			"semi-spacing": ["error", { "before": false, "after": true }]
		}
	},
	{
		files: ['**/*.svelte', '**/*.svelte.ts', '**/*.svelte.js'],
		languageOptions: {
			parserOptions: {
				projectService: true,
				extraFileExtensions: ['.svelte'],
				parser: ts.parser,
				svelteConfig
			}
		}
	}
);
