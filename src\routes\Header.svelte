<script lang="ts">
    import type { Snippet } from 'svelte';
    import { createState, CustomState } from './state.svelte';
    let {
        name
    }: {
        name: string;
    } = $props();

    const myState = createState();
    const myStateBetter = new CustomState();
    /* For use with snippets below
    let {
        children,
        secondChild
    }: {
        children: Snippet;
        secondChild: Snippet;
    } = $props(); */
</script>

<div>
    <h1>
        {name ? name : 'User'}'s Form'
    </h1>

    <!-- Snippet examples
    {@render children()}

    <h3>{@render secondChild('Boks')}</h3> -->

    <!-- ===== Classic Way ====== -->
    <!-- This will only get the value of the state -->
    <button onclick={myState.up}>Classic Way {myState.value}</button>
    <!-- To set the value of the state, you must define a setter in the createState function -->
    <!-- <button onclick={() => {
        myState.value = 10;
    }}>{myState.value}</button> -->

    <!-- ===== Better Way ===== -->
    <button onclick={() => myStateBetter.up()}> Better Way {myStateBetter.value}</button>
    
</div>

<style>
</style>