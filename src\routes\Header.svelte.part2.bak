<script lang="ts">
    import type { Snippet } from 'svelte';
    let {
        name
    }: {
        name: string;
    } = $props();

    /* For use with snippets below
    let {
        children,
        secondChild
    }: {
        children: Snippet;
        secondChild: Snippet;
    } = $props(); */
</script>

<div>
    <h1>
        {name ? name : 'User'}'s Form'
    </h1>

    <!-- Snippet examples
    {@render children()}

    <h3>{@render secondChild('Boks')}</h3> -->
</div>

<style>
</style>