<script lang="ts">
    import Header from './Header.svelte';

    let formState = $state({
        answers: {},
        step: 0,
        error: ""
    })

    const QUESTIONS = [
        {
            question: 'What is your name?',
            id: 'name',
            type: 'text'
        },
        {
            question: 'What is birthday?',
            id: 'birthday',
            type: 'date'
        },
        {
            question: 'What is your favorite color?',
            id: 'color',
            type: 'color'
        }
    ];

    function nextStep(id: string) {
        if (formState.answers[id]) {
            formState.step += 1;
            formState.error = '';
        } else {
            formState.error = 'Please fill out the form input';
        }
    }
</script>

<!-- <Header name = {formState.name}>
    <p>Hello</p>
    {#snippet secondChild(name)}
        <p>Second Child {name}</p>
    {/snippet}
</Header> -->
<Header name = {formState.answers.name}/>
<main>
    {#if formState.step >= QUESTIONS.length}
        <p>Thank you!</p>
    {:else}
        <p>Step: {formState.step + 1}</p>
    {/if}
    <!-- Default way -->
    <!-- {#each QUESTIONS as question (question.id)}
        {@render formStep({ question: "What's your name", id: "name", type: "text"})}
        {/each} 
     -->
    <!-- Destructured way -->
    <!-- {#each QUESTIONS as {id, question, type} (id)}
        {@render formStep({ question, id, type })}
    {/each} -->
    <!-- If the properties are 1:1 -->
    {#each QUESTIONS as question, index (question.id)}
        {#if formState.step === index}
            {@render formStep(question)}
        {/if}
    {/each}

    {#if formState.error !== ''}
        <p class="error">Error: {formState.error}</p>
    {/if}
</main>

<!-- Show the state -->
<!-- {JSON.stringify(formState)} -->

{#snippet formStep({ question, id, type }: {
    type: string;
    id: string;
    question: string
})}
    <article>
        <div>
            <label for={id}>{question}</label>
            <input {type} {id} bind:value={formState.answers[id]}/>
        </div>
        <button onclick={() => nextStep(id)}>Next</button>
    </article>
{/snippet}


<!-- CSS in svelte component is tightly scoped -->
<!-- It is recommended that the styles be put in a global css file -->
<!-- 
    to add global css, use:
    :global(selector) {

    }
-->
<style>
    .error {
        color: red;
    }
</style>